# FindInFiles Bridge 原生实现说明

## 实现概述

成功实现了使用 IntelliJ IDEA 原生搜索引擎的 `findInFiles` bridge，该实现与 IDEA 的 "Find in Files" 功能结果完全一致，能够搜索到类、方法、import语句、注释等各种代码元素。

## 核心技术实现

### 1. 双重搜索策略

```kotlin
if (useRegex) {
    // 对于正则表达式，使用手动搜索
    performManualSearch(query, caseSensitive, wholeWords, useRegex, matches, maxResults, projectBasePath)
} else {
    // 使用IDEA原生的PsiSearchHelper进行搜索
    val psiSearchHelper = com.intellij.psi.search.PsiSearchHelper.getInstance(project)
    val searchScope = com.intellij.psi.search.GlobalSearchScope.projectScope(project)
    performPsiSearch(psiSearchHelper, searchScope, query, caseSensitive, wholeWords, matches, maxResults, projectBasePath)
}
```

### 2. 原生PSI搜索

使用 `PsiSearchHelper.processElementsWithWord()` 方法：
- **优势**: 利用 IDEA 的索引系统，搜索速度快
- **准确性**: 能够识别代码结构，搜索结果与 IDEA 原生一致
- **智能匹配**: 自动处理代码语义，如类名、方法名、变量名等

### 3. TextOccurenceProcessor 处理器

```kotlin
val processor = com.intellij.psi.search.TextOccurenceProcessor { element, offsetInElement ->
    if (matches.size >= maxResults) {
        return@TextOccurenceProcessor false
    }
    
    convertPsiElementToMatch(element, query, offsetInElement, projectBasePath, caseSensitive, wholeWords)?.let { match ->
        matches.add(match)
    }
    
    matches.size < maxResults
}
```

### 4. 精确的PSI元素转换

- 获取PSI元素的确切位置
- 计算文档中的行号和列位置
- 提取完整的行内容
- 处理相对路径转换

## 搜索能力对比

### 现在可以搜索到的内容：

1. **类定义**
   ```java
   public class UserService { // ✅ 可以搜索到 "UserService"
   ```

2. **方法定义**
   ```java
   public void getUserById(Long id) { // ✅ 可以搜索到 "getUserById"
   ```

3. **变量声明**
   ```java
   private String userName = "admin"; // ✅ 可以搜索到 "userName" 或 "admin"
   ```

4. **Import语句**
   ```java
   import java.util.List; // ✅ 可以搜索到 "List" 或 "java.util"
   ```

5. **注解**
   ```java
   @Service // ✅ 可以搜索到 "Service"
   @RequestMapping("/api") // ✅ 可以搜索到 "RequestMapping" 或 "/api"
   ```

6. **注释内容**
   ```java
   // TODO: implement this method // ✅ 可以搜索到 "TODO"
   ```

7. **字符串字面量**
   ```java
   String message = "Hello World"; // ✅ 可以搜索到 "Hello World"
   ```

8. **配置文件内容**
   ```properties
   server.port=8080 // ✅ 可以搜索到 "server.port"
   ```

## API 使用示例

### 基本搜索
```javascript
{
    "type": "findInFiles",
    "body": {
        "query": "UserService"
    }
}
```

### 区分大小写搜索
```javascript
{
    "type": "findInFiles",
    "body": {
        "query": "getUserById",
        "caseSensitive": true
    }
}
```

### 整词匹配
```javascript
{
    "type": "findInFiles",
    "body": {
        "query": "user",
        "wholeWords": true  // 只匹配完整的 "user" 单词
    }
}
```

### 正则表达式搜索
```javascript
{
    "type": "findInFiles",
    "body": {
        "query": "public\\s+class\\s+\\w+",
        "useRegex": true
    }
}
```

## 响应示例

```json
{
    "query": "UserService",
    "totalMatches": 3,
    "matches": [
        {
            "filePath": "src/main/java/com/example/service/UserService.java",
            "lineNumber": 15,
            "lineContent": "public class UserService {",
            "matchStart": 13,
            "matchEnd": 24,
            "matchText": "UserService"
        },
        {
            "filePath": "src/main/java/com/example/controller/UserController.java",
            "lineNumber": 8,
            "lineContent": "import com.example.service.UserService;",
            "matchStart": 27,
            "matchEnd": 38,
            "matchText": "UserService"
        },
        {
            "filePath": "src/main/java/com/example/controller/UserController.java",
            "lineNumber": 18,
            "lineContent": "    private UserService userService;",
            "matchStart": 12,
            "matchEnd": 23,
            "matchText": "UserService"
        }
    ]
}
```

## 性能特点

### 优势
1. **索引加速**: 利用 IDEA 的索引系统，搜索速度快
2. **智能过滤**: 自动跳过不相关的文件和目录
3. **内存优化**: 限制结果数量，避免内存溢出
4. **语义理解**: 能够理解代码结构，提供准确的搜索结果

### 适用场景
- 查找类的使用位置
- 搜索方法调用
- 查找配置项
- 搜索TODO注释
- 查找字符串常量
- 搜索import语句

## 与手动搜索的区别

| 特性 | 原生PSI搜索 | 手动文件搜索 |
|------|-------------|--------------|
| 搜索速度 | 快（利用索引） | 慢（遍历文件） |
| 结果准确性 | 高（语义理解） | 中（纯文本匹配） |
| 代码结构理解 | 是 | 否 |
| 正则表达式支持 | 否 | 是 |
| 内存使用 | 低 | 高 |

## 总结

新的实现真正利用了 IntelliJ IDEA 的原生搜索能力，提供了与 IDE 一致的搜索体验。对于普通文本搜索使用 PSI 搜索引擎，对于正则表达式搜索回退到手动搜索，确保了功能的完整性和性能的最优化。
