package com.think1024.tocodesign.ideaplugin.webview

import TocoBrowserFactory.createBrowserWithCookies
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import com.intellij.ide.ui.LafManager
import com.intellij.ide.ui.LafManagerListener
import com.intellij.notification.Notification
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.editor.colors.EditorColorsListener
import com.intellij.openapi.editor.colors.EditorColorsManager
import com.intellij.openapi.editor.colors.EditorColorsScheme
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.Messages
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.util.IconLoader
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.ui.jcef.JBCefBrowser
import com.intellij.ui.jcef.JBCefClient.Properties
import com.intellij.util.ui.JBUI
import com.jetbrains.cef.JCefAppConfig
import com.think1024.tocodesign.ideaplugin.services.CodeFileService
import com.think1024.tocodesign.ideaplugin.services.ConfigService
import com.think1024.tocodesign.ideaplugin.services.NotificationService
import com.think1024.tocodesign.ideaplugin.services.StructureAnalyzerService
import com.think1024.tocodesign.ideaplugin.services.codebase.CodeBaseSearchManager
import com.think1024.tocodesign.ideaplugin.services.locator.LocatorService
import com.think1024.tocodesign.ideaplugin.settings.ApplicationPluginSettings
import com.think1024.tocodesign.ideaplugin.toco.TocoWebViewLauncher
import com.think1024.tocodesign.ideaplugin.ui.LoginHandler
import com.think1024.tocodesign.ideaplugin.utils.CookieManager
import com.think1024.tocodesign.ideaplugin.utils.IdeaConfig
import com.think1024.tocodesign.ideaplugin.utils.LanguageManager.getI18nString
import com.think1024.tocodesign.ideaplugin.utils.getStringOrNull
import kotlinx.coroutines.*
import org.cef.CefApp
import org.cef.browser.CefBrowser
import org.cef.browser.CefFrame
import org.cef.browser.CefMessageRouter
import org.cef.handler.CefLoadHandlerAdapter
import org.cef.handler.CefRequestHandler
import org.cef.handler.CefRequestHandlerAdapter
import org.cef.network.CefCookieManager
import org.cef.network.CefRequest
import org.jetbrains.concurrency.AsyncPromise
import org.jetbrains.concurrency.Promise
import java.awt.*
import java.awt.event.*
import java.lang.reflect.Method
import java.net.HttpCookie
import java.net.URL
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.regex.Pattern
import javax.swing.*
import javax.swing.event.PopupMenuEvent
import javax.swing.event.PopupMenuListener
import javax.swing.plaf.basic.BasicMenuItemUI
import kotlin.coroutines.CoroutineContext

const val TOCO_MESSAGE_REQUEST_TYPE = "tocoIdeaPlugin-request"
const val TOCO_MESSAGE_RESPONSE_TYPE = "tocoIdeaPlugin-response"

data class IdeaRequestMessage<T>(
    val id: String,
    val type: String,
    val body: T? = null,
    val needResponse: Boolean? = false
)

data class IdeaResponseMessage<T>(
    val id: String,
    val type: String,
    val code: Int,
    val body: T? = null
)

enum class MenuItemType {
    NORMAL,
    SEPARATOR,
    CHECKBOX
}

data class MenuItem(
    val id: String,
    val type: MenuItemType? = MenuItemType.NORMAL, // 默认值 NORMAL
    val label: String,
    val enabled: Boolean, // 默认值 true
    val accelerator: String? = null,
    val visible: Boolean, // 默认值 true
    val checked: Boolean, // 默认值 false
    val uuid: String,
    val icon: String? = null,
    val submenu: List<MenuItem>? = null // 递归定义
)

enum class FormItemType {
    INPUT
}

data class FormRule (
    val pattern: Pattern,
    val message: String
)

data class FormItem(
    val name: String,
    val label: String,
    val type: FormItemType,
    val rules: List<FormRule>?
)

class TitleMenuItemUI : BasicMenuItemUI() {
    override fun paintBackground(g: Graphics?, menuItem: JMenuItem?, bgColor: Color?) {
        // 禁止title渲染高亮色
    }

    override fun paintText(g: Graphics?, menuItem: JMenuItem?, textRect: Rectangle?, text: String?) {
        // 替换 SwingUtilities2 的使用
        if (g == null || menuItem == null || textRect == null || text == null) return

        val fm = menuItem.getFontMetrics(menuItem.font)
        val menuIndex = menuItem.displayedMnemonicIndex

        // 替换 SwingUtilities2.drawStringUnderlineCharAt
        g.font = menuItem.font
        g.color = menuItem.foreground

        // 绘制文本
        g.drawString(text, textRect.x, textRect.y + fm.ascent)

        // 如果有助记符，绘制下划线
        if (menuIndex >= 0 && menuIndex < text.length) {
            val underlineChar = text[menuIndex]
            val charWidth = fm.charWidth(underlineChar)
            val underlineX = textRect.x + fm.stringWidth(text.substring(0, menuIndex))
            val underlineY = textRect.y + fm.ascent + 1
            g.drawLine(underlineX, underlineY, underlineX + charWidth, underlineY)
        }
    }

    override fun doClick(msm: MenuSelectionManager?) {
        // 禁用点击
    }
}

data class TocoBrowserConfig(
    var isFrame: Boolean,
    var isOSR: Boolean,
    val file: VirtualFile? = null
)

class TocoThemeChangeListener(private val browser: TocoBrowser): LafManagerListener {
    override fun lookAndFeelChanged(source: LafManager) {
        WebViewManager.setBrowserTheme(browser, IdeaConfig.getTheme(source.currentUIThemeLookAndFeel))
    }
}

class TocoEditorSchemeListener(private val browser: TocoBrowser): EditorColorsListener {
    override fun globalSchemeChange(scheme: EditorColorsScheme?) {
        WebViewManager.setBrowserTheme(browser, IdeaConfig.getEditorTheme(scheme))
    }
}

@OptIn(DelicateCoroutinesApi::class)
class TocoBrowser(val project: Project? = null, val url: String, val config: TocoBrowserConfig) {
    val browser: JBCefBrowser
    private var myDevtoolsFrame: JDialog? = null
    private var myDevTools: JBCefBrowser? = null
    private var pageLoaded = false
    private var receivedInitPage = false
    private var modified = false
    private var onInitPage: (() -> Unit)? = null
    private var onModifiedChanged: ((modified: Boolean) -> Unit)? = null
    private var messageRouter: CefMessageRouter? = null

    private var eventHandlers: Map<String, MutableList<(data: JsonObject) -> Unit>> = mutableMapOf()
    private val notifications: MutableMap<String, Notification> = mutableMapOf()
    private val logger = Logger.getInstance(TocoBrowser::class.java)

    fun addEventListener(event: String, handler: (data: JsonObject) -> Unit) {
        val list = eventHandlers[event] ?: mutableListOf()
        list.add(handler)
        eventHandlers = eventHandlers + (event to list)
    }

//    fun removeEventListener(event: String, handler: (data: JsonObject) -> Unit) {
//        val list = eventHandlers[event] ?: return
//        list.remove(handler)
//        eventHandlers = eventHandlers + (event to list)
//    }

    fun setOnInitPageCallback(callback: () -> Unit) {
        onInitPage = callback
//        println("000, setInitPage: ${browser.hashCode()} ${browser.cefBrowser.url} onInitPage = $onInitPage")
        maybeTriggerInitPage()
    }

    fun setOnModifiedChanged(callback: (modified: Boolean) -> Unit) {
        onModifiedChanged = callback
    }

    private fun maybeTriggerInitPage() {
        if (pageLoaded && receivedInitPage) {
//            println("222, maybeTriggerInitPage: ${browser.hashCode()} ${browser.cefBrowser.url} onInitPage = $onInitPage")
            onInitPage?.invoke()
            // 只调用一次后清理掉
            onInitPage = null
        }
    }



    private fun registerAppSchemeHandler() {
        val host = ApplicationPluginSettings.getInstance().frontendHost
        val url = URL(host)
        CefApp.getInstance().registerSchemeHandlerFactory(
            url.protocol,
            url.host,
            TocoWebViewSchemeHandlerFactory()
        )
    }

    private fun executeJavaScriptAsync(script: String): Promise<Nothing> {
        val promise = AsyncPromise<Nothing>()

        // 使用自定义协程作用域确保正确取消
        val coroutineScope = object : CoroutineScope {
            override val coroutineContext: CoroutineContext = Dispatchers.Main
        }

        coroutineScope.launch {
            try {
                // 在IO调度器执行阻塞操作（实际执行JS）
                withContext(Dispatchers.IO) {
                    browser.cefBrowser.executeJavaScript(script, "", 0)
                }
                promise.setResult(null)
            } catch (_: CancellationException) {
                promise.cancel() // 协程取消时取消Promise
            } catch (t: Throwable) {
                promise.setError(t)
            }
        }

        return promise
    }

    private fun sendResponseToWebview(id: String, type: String, body: Any?) {
        if (browser.isDisposed) {
            println("$type, send response to disposed webview :${browser.hashCode()}")
            return
        }
        val response = IdeaResponseMessage(
            id = id,
            type = type,
            code = 200,
            body = Gson().toJsonTree(body)
        )
        val jsonResponse = Gson().toJson(
            mapOf(
                "type" to TOCO_MESSAGE_RESPONSE_TYPE,
                "message" to response
            )
        )

        try {
            executeJavaScriptAsync("""window.postMessage($jsonResponse, "*");""")
                .onError { println("Failed to send response: ${it.message}") }
        } catch (e: Exception) {
            println("Error sending $type response: $e")
        }
    }

    private fun parseMenuItem(obj: JsonObject): MenuItem {
        return MenuItem(
            id = obj.get("id").asString,
            type = obj.get("type")?.asString?.let { type ->
                when (type) {
                    "separator" -> MenuItemType.SEPARATOR
                    "checkbox" -> MenuItemType.CHECKBOX
                    else -> MenuItemType.NORMAL
                }
            },
            label = obj.get("label").asString,
            enabled = obj.get("enabled")?.asBoolean != false,
            accelerator = obj.get("accelerator")?.asString,
            visible = obj.get("visible")?.asBoolean != false,
            checked = obj.get("checked")?.asBoolean == true,
            uuid = obj.get("uuid").asString,
            icon = obj.get("icon")?.asString,
            submenu = obj.get("submenu")?.asJsonArray?.map { subItem ->
                parseMenuItem(subItem.asJsonObject)
            })
    }

    private fun handleMenu(data: JsonObject) {
        val id = data.get("id").asString
        val items = data.get("itms").asJsonArray
        val menuItems = items.map { item ->
            val obj = item.asJsonObject
            parseMenuItem(obj)
        }

        val position = data.get("position").asJsonObject
        val x = position.get("x").asInt
        val y = position.get("y").asInt

        showPopupMenu(id, menuItems, x, y)
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun handleToast(data: JsonObject, show: Boolean) {
        val id = data.get("id").asString
        if (show) {
            val text = data.get("text").asString
            val type = data.get("type").asString
            val duration = data.get("duration").asInt
            val notification = NotificationService.instance.notify(
                "",
                text,
                project,
                NotificationService.instance.getNotificationType(type),
                NotificationService.NotificationGroup.TOCO,
                duration,
            )
            if (notification != null) {
                notification.whenExpired {
                    GlobalScope.launch {
                        sendToWebview("toast-hidden:$id")
                    }
                    notifications.remove(id)
                }
                notifications[id] = notification
            }
        } else {
            val notification = notifications[id]
            notification?.expire()
            notifications.remove(id)
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun handleConfirm(data: JsonObject) {
        val id = data.get("id").asString
        val title = data.get("title").asString
        val content = data.get("content").asString
        val okText = data.get("okText").asString

        val dialog = JDialog(null as Frame?, title, true)
        dialog.layout = BorderLayout(10, 10)
        dialog.setSize(400, 200)
        dialog.setLocationRelativeTo(null)

        val buttonPanel = JPanel()
        buttonPanel.layout = FlowLayout(FlowLayout.RIGHT)

        val saveButton = JButton(okText)
        saveButton.addActionListener {
            GlobalScope.launch {
                sendToWebview("confirm-hidden:$id", true)
            }
            dialog.dispose()
        }

        val cancelButton = JButton(getI18nString("button.cancel"))
        cancelButton.addActionListener {
            GlobalScope.launch {
                sendToWebview("confirm-hidden:$id", false)
            }
            dialog.dispose()
        }

        buttonPanel.add(saveButton)
        buttonPanel.add(cancelButton)

        val contentPanel = JPanel()
        contentPanel.layout = GridBagLayout()
        val gbc = GridBagConstraints()
        gbc.insets = JBUI.insets(5)
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.weightx = 1.0
        val label = JLabel(content)
        contentPanel.add(label, gbc)
        dialog.add(JScrollPane(contentPanel), BorderLayout.CENTER)
        dialog.add(buttonPanel, BorderLayout.SOUTH)

        SwingUtilities.invokeLater {
            dialog.isVisible = true
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun showPopupMenu(id: String, menuItems: List<MenuItem>, x: Int, y: Int) {
        val popupMenu = JPopupMenu()

        popupMenu.addPopupMenuListener(object : PopupMenuListener {
            override fun popupMenuWillBecomeVisible(e: PopupMenuEvent?) {}
            override fun popupMenuCanceled(e: PopupMenuEvent?) {
                GlobalScope.launch {
                    sendToWebview("menu-closed:$id")
                }
            }
            override fun popupMenuWillBecomeInvisible(e: PopupMenuEvent) {}
        })

        // 递归构建菜单项
        fun buildMenu(parent: JComponent, items: List<MenuItem>) {
            items.forEach { item ->
                if (!item.visible) return@forEach // 跳过不可见项

                when (item.type) {
                    MenuItemType.SEPARATOR -> {
                        if (parent is JPopupMenu || parent is JMenu) {
                            (parent as JPopupMenu).addSeparator()
                        }
                    }
                    else -> {
                        // 创建菜单项（根据 checked 属性选择类型）
                        val isTitle = item.id == "__title"
                        val menuItem = if (!isTitle && item.type == MenuItemType.CHECKBOX) {
                            JCheckBoxMenuItem(item.label, item.checked)
                        } else {
                            JMenuItem(item.label)
                        }

                        item.icon?.let { iconPath ->
                            try {
                                val icon = IconLoader.getIcon("/icons/menu/${item.icon}.svg", TocoBrowser::class.java)
                                menuItem.icon = icon
                            } catch (_: Exception) {
                                println("Failed to load icon: $iconPath")
                            }
                        }

                        // 设置属性
                        if (isTitle) {
                            menuItem.setUI(TitleMenuItemUI())
                            parent.add(menuItem)
                        } else {
                            menuItem.isEnabled = item.enabled

                            // 先不管快捷键，可能会和idea的快捷键冲突
//                            item.accelerator?.let { accel ->
//                                // TODO: 兼容electron的快捷键格式
//                                menuItem.accelerator = KeyStroke.getKeyStroke(accel)
//                            }

                            // 添加点击事件
                            menuItem.addActionListener { _ ->
                                GlobalScope.launch {
                                    // 第一个请求需要needresponse来前后调用的顺序
                                    sendToWebview("menu-item:$id", item.uuid, true)
                                    sendToWebview("menu-closed:$id")
                                }
                            }

                            // 处理子菜单
                            item.submenu?.let { submenuItems ->
                                val subMenu = JMenu(item.label)
                                subMenu.isEnabled = item.enabled
                                buildMenu(subMenu, submenuItems)
                                parent.add(subMenu)
                            } ?: parent.add(menuItem)
                        }
                    }
                }
            }
        }

        // 构建弹出菜单
        buildMenu(popupMenu, menuItems)

        // 在 EDT 上显示菜单（确保线程安全）
        SwingUtilities.invokeLater {
            // 如果有 browser，相对于其组件显示
            browser.component.let { component ->
                popupMenu.show(component, x, y)
            }
        }
    }

    private fun handleFormDialog(data: JsonObject) {
        val id = data.get("id").asString
        val title = data.get("title").asString
        val items = data.get("itms").asJsonArray
        val formItems = items.map { item ->
            val obj = item.asJsonObject
            FormItem(
                name = obj.get("name").asString,
                label = obj.get("label").asString,
                rules = obj.get("rules")?.asJsonArray?.map { ruleItem ->
                    val rule = ruleItem.asJsonObject
                    FormRule(
                        pattern = Pattern.compile(rule.get("pattern").asString),
                        message = rule.get("message").asString
                    )
                },
                type = obj.get("type").asString.let { type ->
                    when (type) {
                        "input" -> FormItemType.INPUT
                        else -> FormItemType.INPUT
                    }
                },
            )
        }
        showFormDialog(id, formItems, title)
    }

    private fun handleAddLog(data: JsonObject) {
        val level = data.get("level")?.asString ?: "info"
        val logMessage = data.get("message")?.asString ?: ""

        when (level.lowercase()) {
            "debug" -> logger.debug(logMessage)
            "info" -> logger.info(logMessage)
            "warn" -> logger.warn(logMessage)
            "error" -> {
                val errorDetails = data.get("error")?.asString
                if (errorDetails != null) {
                    logger.error("$logMessage - Error details: $errorDetails")
                } else {
                    logger.error(logMessage)
                }
            }
            else -> logger.info(logMessage) // 默认使用info级别
        }
    }

    private fun showFormDialog(
        id: String,
        formItems: List<FormItem>,
        title: String = "Form",
    ) {
        val formPanel = JPanel()
        formPanel.layout = GridBagLayout()
        val gbc = GridBagConstraints()
        gbc.insets = JBUI.insets(5)
        gbc.fill = GridBagConstraints.HORIZONTAL
        gbc.weightx = 1.0

        val inputFields = mutableMapOf<String, JTextField>()

        formItems.forEachIndexed { index, item ->
            when (item.type) {
                FormItemType.INPUT -> {
                    gbc.gridx = 0
                    gbc.gridy = index
                    gbc.weightx = 0.3
                    formPanel.add(JLabel("${item.label}:"), gbc)

                    val textField = JTextField(20)
                    inputFields[item.name] = textField
                    gbc.gridx = 1
                    gbc.weightx = 0.7
                    formPanel.add(textField, gbc)
                }
            }
        }

        val dialog = JDialog(null as Frame?, title, true)
        dialog.layout = BorderLayout(10, 10)
        dialog.setSize(400, 300)
        dialog.setLocationRelativeTo(null)

        val buttonPanel = JPanel()
        buttonPanel.layout = FlowLayout(FlowLayout.RIGHT)

        val saveButton = JButton(getI18nString("button.save"))
        saveButton.addActionListener {
            val formData = inputFields.mapValues { it.value.text }
            val items = inputFields.keys.map { key ->
                formItems.find { it.name == key }
            }
            items.forEach { item ->
                if (item?.rules != null && item.rules.isNotEmpty()) {
                    item.rules.forEach { rule ->
                        formData[item.name]?.let { it1 ->
                            if (!rule.pattern.matcher(it1).matches()) {
                                JOptionPane.showMessageDialog(
                                    dialog,
                                    rule.message,
                                    "Validation Error",
                                    JOptionPane.ERROR_MESSAGE
                                )
                                return@addActionListener // 阻止保存
                            }
                        }
                    }
                }
            }

            GlobalScope.launch {
                sendToWebview("form-dialog-close:$id", formData)
            }
            dialog.dispose()
        }

        val cancelButton = JButton(getI18nString("button.cancel"))
        cancelButton.addActionListener {
            GlobalScope.launch {
                sendToWebview("form-dialog-close:$id")
            }
            dialog.dispose()
        }

        buttonPanel.add(saveButton)
        buttonPanel.add(cancelButton)

        dialog.add(JScrollPane(formPanel), BorderLayout.CENTER)
        dialog.add(buttonPanel, BorderLayout.SOUTH)

        SwingUtilities.invokeLater {
            dialog.isVisible = true
        }
    }

    init {
        if (ApplicationPluginSettings.getInstance().getIsDev()) {
            val cefAppConfig = JCefAppConfig.getInstance()
            cefAppConfig.appArgsAsList.add("--disable-web-security")
            cefAppConfig.appArgsAsList.add("--ignore-certificate-errors")
        }

        if (isGreaterThan2025()) {
            // 2025版本不设置成false会导致OSR强制开启
            System.setProperty("jcef.remote.enabled", "false")
        }

        this.browser = JBCefBrowser.createBuilder()
            .setCreateImmediately(config.isOSR) // 非离屏渲染如果立刻create在linux会导致内容不显示
            .setOffScreenRendering(config.isOSR)
            .build()
            .apply {
                jbCefClient.setProperty(Properties.JS_QUERY_POOL_SIZE, 200)
                // 处理toolwindow页面会抢夺tab页焦点的问题，默认无法被聚焦
                // 当鼠标点击时允许聚焦，失去焦点就不允许聚焦
                cefBrowser.uiComponent.isFocusable = !config.isFrame

                if (config.isFrame) {
                    cefBrowser.uiComponent.addMouseListener(object : MouseAdapter() {
                        override fun mousePressed(e: MouseEvent?) {
                            super.mousePressed(e)
                            if (!cefBrowser.uiComponent.isFocusable) {
                                cefBrowser.uiComponent.isFocusable = true
                                cefBrowser.uiComponent.requestFocusInWindow()
                            }
                        }
                    })
                    cefBrowser.uiComponent.addFocusListener(object : FocusAdapter() {
                        override fun focusLost(e: FocusEvent) {
                            super.focusLost(e)
                            if (cefBrowser.uiComponent.isFocusable) {
                                cefBrowser.uiComponent.isFocusable = false
                            }
                        }
                    })
                }
            }

        try {
            val cookieString = CookieManager.getCookies()
            val cookies = mutableMapOf<String, String>()
            if (cookieString.isNotEmpty()) {
                val parsedCookies = HttpCookie.parse(cookieString)
                for (cookie in parsedCookies) {
                    cookies[cookie.name] = cookie.value
                }
            } else {
                // 如果 cookie 为空，设置 U_TOKEN 为空值并使其过期
                cookies[CookieManager.U_TOKEN] = ""
            }

            createBrowserWithCookies(url, cookies, browser)
        } catch (e: Exception) {
            println("Error parsing cookies: $e")
        }

        registerAppSchemeHandler()
        browser.loadURL(url)

        // Listen for events sent from browser
        val that = this
        val handler = TocoMessageHandler  { msg: String? ->
            val json: JsonObject = JsonParser.parseString(msg).asJsonObject
            val messageType = json.get("messageType").asString
            val data = json.get("data").asJsonObject
            when (messageType) {
                TOCO_MESSAGE_REQUEST_TYPE -> {
                    val id = data.get("id").asString
                    val type = data.get("type").asString
                    val bodyElement = data.get("body")
                    val body = if (bodyElement != null && bodyElement.isJsonObject) {
                        bodyElement.asJsonObject
                    } else {
                        JsonObject() // 或者 null，取决于你业务逻辑
                    }

                    GlobalScope.launch {
                        var shouldSendResponse = true
                        val result = when (type) {
                            "getConfig" -> project?.getService(ConfigService::class.java)?.getConfig(project)
                            "getFileList" -> project?.getService(StructureAnalyzerService::class.java)?.getFileList(project, body.get("keyword").asString)
                            "getMethod" -> project?.getService(StructureAnalyzerService::class.java)?.getMethod(project, body.get("className").asString, body.get("methodName").asString)
                            "openPage" -> TocoWebViewLauncher.openPage(project, body.get("url").asString, body.get("name").asString, body.get("type").asString, false, body.get("method").asString)
                            "pushStore" -> WebViewManager.syncStoreAction(project, body, that)
                            "pullStore" -> WebViewManager.getStore(project, that)
                            "initPage" -> {
//                                println("111, initPage:${browser.hashCode()} ${browser.cefBrowser.url}")
                                receivedInitPage = true
                                maybeTriggerInitPage()
                            }
                            "login" -> LoginHandler.getInstance(project).handleLogin(body.get("url").asString)
                            "menu-open" -> handleMenu(body)
                            "show-toast" -> handleToast(body, true)
                            "hide-toast" -> handleToast(body, false)
                            "show-confirm" -> handleConfirm(body)
                            "form-dialog" -> handleFormDialog(body)
                            "close-tab" -> {
                                if(config.file != null && project != null) {
                                    ApplicationManager.getApplication().invokeLater {
                                        FileEditorManager.getInstance(project).closeFile(config.file)
                                    }
                                }
                                null
                            }
                            "getCodeFiles" -> {
                                val filePaths = body.get("filePaths").asJsonArray.map { it.asString }.toTypedArray()
                                project?.getService(CodeFileService::class.java)?.getFilesContentAsync(filePaths) { result ->
                                    sendResponseToWebview(id, type, result)
                                }
                                shouldSendResponse = false  // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "getDirectoryFileList" -> {
                                val result = project?.getService(CodeFileService::class.java)?.getFilesInFolder(body.get("directory").asString, body.get("recursive").asBoolean)
                                sendResponseToWebview(id, type, result)
                                shouldSendResponse = false  // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "readFile" -> {
                                val filePath = body.get("path").asString
                                val startLine = body.getStringOrNull("startLine")?.toIntOrNull()
                                val endLine = body.getStringOrNull("endLine")?.toIntOrNull()
                                val result = project?.getService(CodeFileService::class.java)?.readFile(filePath, startLine, endLine)
                                sendResponseToWebview(id, type, result)
                                shouldSendResponse = false  // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "readFiles" -> {
                                val readFilesRequest = body.get("readFilesParam").asJsonArray.map { it.asJsonObject }.map { readObj ->
                                    CodeFileService.FileReadRequest(
                                        filePath = readObj.get("filePath").asString,
                                        startLine = readObj.getStringOrNull("startLine")?.toIntOrNull(),
                                        endLine = readObj.getStringOrNull("endLine")?.toIntOrNull()
                                    )
                                }

                                val result = project?.getService(CodeFileService::class.java)?.readFiles(readFilesRequest)
                                sendResponseToWebview(id, type, result)
                                shouldSendResponse = false  // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "readClassMethod" -> {
                                val filePath = body.get("file").asString
                                val methodName = body.get("methodPath").asString
                                val result = project?.getService(CodeFileService::class.java)?.readClassMethod(filePath, methodName)
                                sendResponseToWebview(id, type, result)
                                shouldSendResponse = false  // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "createFile" -> {
                                val filePath = body.get("targetPath").asString
                                val content = body.getStringOrNull("content")
                                val result = project?.getService(CodeFileService::class.java)?.createFile(filePath, content)
                                sendResponseToWebview(id, type, result)
                                shouldSendResponse = false  // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "editFile" -> {
                                val filePath = body.getStringOrNull("targetFile") ?: ""
                                val content = body.getStringOrNull("codeEdit") ?: ""
                                val result = project?.getService(CodeFileService::class.java)?.editFile(filePath, content)
                                sendResponseToWebview(id, type, result)
                                shouldSendResponse = false  // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "codebaseSearch" -> {
                                val query = body.get("query").asString
                                if (query.isEmpty()) {
                                    sendResponseToWebview(id, type, emptyList<Any>())
                                } else {
                                    val results = project?.getService(CodeBaseSearchManager::class.java)?.search(query)
                                    sendResponseToWebview(id, type, results)
                                }

                                shouldSendResponse = false  // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "buildProject" -> {
                                project?.getService(CodeFileService::class.java)?.buildProject { result ->
                                    sendResponseToWebview(id, type, result)
                                }
                                shouldSendResponse = false  // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "tab-modify" -> {
                                val newModified = body.get("modify").asBoolean
                                if (newModified != modified) {
                                    modified = newModified
                                    onModifiedChanged?.invoke(modified)
                                } else {
                                    // do nothing
                                }
                            }
                            "locator" -> {
                                if (project != null) {
                                    shouldSendResponse = false
                                    LocatorService.getInstance(project).handleLocatorMessage(body, "locate") {
                                        sendResponseToWebview(id, type, it)
                                    }
                                } else {

                                }
                            }
                            "findJavaClass" -> {
                                val className = body.getStringOrNull("className") ?: ""
                                val result = project?.getService(CodeFileService::class.java)?.findJavaClassAndContent(className)
                                sendResponseToWebview(id, type, result)
                                shouldSendResponse = false // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "showDiff" -> {
                                val filePath = body.getStringOrNull("filePath") ?: ""
                                val originContent = body.getStringOrNull("originContent")
                                val result = project?.getService(CodeFileService::class.java)?.showOriginalDiff(filePath, originContent)
                                sendResponseToWebview(id, type, result)
                                shouldSendResponse = false // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "showInlineDiff" -> {
                                val filePath = body.getStringOrNull("filePath") ?: ""
                                val originContent = body.getStringOrNull("originContent")
                                val result = project?.getService(CodeFileService::class.java)?.showInlineDiff(filePath, originContent)
                                sendResponseToWebview(id, type, result)
                                shouldSendResponse = false // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "acceptChangesForAllFiles" -> {
                                val filePaths = body.get("filePaths").asJsonArray.map { it.asString }.toTypedArray()
                                val result = project?.getService(CodeFileService::class.java)?.acceptChangesForAllFiles(filePaths)
                                sendResponseToWebview(id, type, result)
                                shouldSendResponse = false // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "acceptChangesForSingleFile" -> {
                                val filePath = body.get("filePath").asString
                                val result = project?.getService(CodeFileService::class.java)?.acceptChangesForSingleFile(filePath)
                                sendResponseToWebview(id, type, result)
                                shouldSendResponse = false // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "discardChangesForSingleFile" -> {
                                val filePath = body.get("filePath").asString
                                val result = project?.getService(CodeFileService::class.java)?.discardChangesForSingleFile(filePath)
                                sendResponseToWebview(id, type, result)
                                shouldSendResponse = false // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "discardChangesForAllFiles" -> {
                                val filePaths = body.get("filePaths").asJsonArray.map { it.asString }.toTypedArray()
                                val result = project?.getService(CodeFileService::class.java)?.discardChangesForAllFiles(filePaths)
                                sendResponseToWebview(id, type, result)
                                shouldSendResponse = false // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "addLog" -> {
                                handleAddLog(body)
                                null // 日志记录不需要返回值
                            }
                            "listOpenedFiles" -> {
                                val result = project?.getService(CodeFileService::class.java)?.listOpenedFiles()
                                sendResponseToWebview(id, type, result)
                                shouldSendResponse = false // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            "findInFiles" -> {
                                val query = body.getStringOrNull("query") ?: ""
                                val caseSensitive = body.get("caseSensitive")?.asBoolean ?: false
                                val wholeWords = body.get("wholeWords")?.asBoolean ?: false
                                val useRegex = body.get("useRegex")?.asBoolean ?: false

                                val result = project?.getService(CodeFileService::class.java)?.findInFiles(
                                    query, caseSensitive, wholeWords, useRegex
                                )
                                sendResponseToWebview(id, type, result)
                                shouldSendResponse = false // 标记不需要在外部发送响应
                                null // 返回 null 表示响应将异步发送
                            }
                            else -> {
                                val handlers = eventHandlers[type]
                                handlers?.forEach { it(body) } ?: mapOf("message" to "Unhandled type: $type")
                            }
                        }

                        if (shouldSendResponse) {
                            sendResponseToWebview(id, type, result)
                        }
                    }
                }
                TOCO_MESSAGE_RESPONSE_TYPE -> {
                    val jsonObject = json.get("data").asJsonObject
                    val id = jsonObject.get("id")?.asString
                    val type = jsonObject.get("type").asString
//                    println("TOCO_MESSAGE_RESPONSE_TYPE id: $id")
//                    println("TOCO_MESSAGE_RESPONSE_TYPE type: $type")

                    id?.let {
                        val deferred = pendingResponseMap.remove(it)
                        deferred?.complete(jsonObject) ?: println("未找到对应的等待响应: $id")
                    }
                }
            }

            null
        }

        val routerConfig = CefMessageRouter.CefMessageRouterConfig("tocoQuery", "tocoQueryCancel")
        messageRouter = CefMessageRouter.create(routerConfig, handler)
        browser.cefBrowser.client.addMessageRouter(messageRouter)

        browser.jbCefClient.addRequestHandler(object : CefRequestHandlerAdapter() {
            override fun onRenderProcessTerminated(browser: CefBrowser?, status: CefRequestHandler.TerminationStatus?) {
                super.onRenderProcessTerminated(browser, status)
                println("Render process terminated: ${browser?.url} with status: $status")
//                Messages.showYesNoDialog("原因:$status, ${browser?.url ?: ""}", "JCEF进程结束", null)
                ApplicationManager.getApplication().invokeLater {
                    val result = Messages.showYesNoDialog(project,
                        "reason = $status, url = ${browser?.url ?: ""}",
                        "Browser Render Process Terminated",
                        "Reload",
                        "Cancel",
                        Messages.getWarningIcon()
                    )
                    if (result == Messages.YES) {
                        reload()
                    }
                }
            }
        }, browser.cefBrowser)

        // 在页面加载完成后注入 JS 变量
        browser.jbCefClient.addLoadHandler(object : CefLoadHandlerAdapter() {
            override fun onLoadStart(browser: CefBrowser?, frame: CefFrame?, transitionType: CefRequest.TransitionType?) {
                if (browser != null) {
                    injectInfoIfNeeded(browser)
                }
                val isDark = getTheme() == "DARK"
                val background = if (isDark) "black" else "white"
                frame?.executeJavaScript(
                    "if (document.body) document.body.style.backgroundColor = \"$background\";",
                    "about:blank",
                    0
                )
            }
            override fun onLoadingStateChange(
                browser: CefBrowser?,
                isLoading: Boolean,
                canGoBack: Boolean,
                canGoForward: Boolean
            ) {
                if (!isLoading && browser != null) {
                    try {
//                        println("pageLoaded:${browser.hashCode()}")
                        pageLoaded = true
                        injectInfoIfNeeded(browser)
                        maybeTriggerInitPage()
                    } catch (e: Exception) {
                        println("加载状态变化处理时出错: ${e.message}")
                    }
                }
            }
            override fun onLoadEnd(
                browser: CefBrowser?,
                frame: CefFrame?,
                httpStatusCode: Int
            ) {
                if (frame?.isValid == true) {
                    frame.executeJavaScript(
                        """
                  window.document.documentElement.dataset.theme = '${getTheme()}';
                """.trimIndent(),
                        frame.url,
                        0
                    )
                }
            }

        }, browser.cefBrowser)

        WebViewManager.register(project, this)

        if (config.isFrame) {
            ApplicationManager.getApplication().messageBus.connect().subscribe(
                LafManagerListener.TOPIC,
                TocoThemeChangeListener(this)
            )
        } else {
            ApplicationManager.getApplication().messageBus.connect().subscribe(
                EditorColorsManager.TOPIC,
                TocoEditorSchemeListener(this)
            )
        }
    }

    private fun getTheme(): String {
        val theme = if (config.isFrame) IdeaConfig.getCurrentTheme() else IdeaConfig.getCurrentEditorTheme()
        return theme
    }

    private fun injectInfoIfNeeded(browser: CefBrowser) {
        try {
            val script = """
                    if (typeof window.tocoMainFrame !== 'boolean') {
                        window.tocoMainFrame = ${if (config.isFrame) "true" else "false"}
                    }
                    """.trimIndent()
            GlobalScope.launch {
                browser.executeJavaScript(script, browser.url, 0)
            }
        } catch (e: Exception) {
            println("执行 JavaScript 时出错: ${e.message}")
        }
    }

    suspend fun sendToWebview(
        type: String,
        body: Any? = null,
        needResponse: Boolean? = false,
        eventId: String? = null,
    ): JsonObject? {
        if (browser.isDisposed) {
            println("$type, send request to disposed webview :${browser.hashCode()} ${browser.cefBrowser.url}")
            return null
        }
        val id = eventId ?: UUID.randomUUID().toString()
        val message = IdeaRequestMessage(
            id,
            type,
            body,
            needResponse
        )
        // response map add handler
        val jsonData = Gson().toJson(
            mapOf(
                "type" to TOCO_MESSAGE_REQUEST_TYPE,
                "message" to message,
            )
        )
        val deferred = if (needResponse == true) {
            if (pendingResponseMap[id] !== null) {
                pendingResponseMap[id]
            } else {
                CompletableDeferred<JsonObject>().also {
                    pendingResponseMap[id] = it
                }
            }
        } else null
        try {
            executeJavaScriptAsync("""window.postMessage($jsonData, "*");""").onError {
                println("JS 执行失败: ${it.message} ${browser.hashCode()} ${browser.cefBrowser.url}")
                pendingResponseMap.remove(id)?.completeExceptionally(it)
            }
        } catch (e: Exception) {
            println("Error sending $type request: $e")
        }

        return deferred?.await()
    }

    fun sendToWebviewDirectly(
        type: String,
        body: Any? = null,
        eventId: String? = null,
    ) {
        if (browser.isDisposed) {
            println("$type, send request directly to disposed webview :${browser.hashCode()} ${browser.cefBrowser.url}")
            return
        }
        val id = eventId ?: UUID.randomUUID().toString()
        val message = IdeaRequestMessage(
            id,
            type,
            body,
        )
        // response map add handler
        val jsonData = Gson().toJson(
            mapOf(
                "type" to TOCO_MESSAGE_REQUEST_TYPE,
                "message" to message,
            )
        )
        try {
            browser.cefBrowser.executeJavaScript("""window.postMessage($jsonData, "*");""", "", 0)
        } catch (e: Exception) {
            println("Error directly sending $type request: $e")
        }
    }

    private fun isGreaterThan2025(): Boolean {
        return ApplicationInfo.getInstance().build.baselineVersion >= 250
    }

    private fun patchDevTool(devTools: CefBrowser) {
        if (isGreaterThan2025()) {
            val uiComponent = devTools.uiComponent
            try {
                // Get the JBCefOsrComponent class
                val jbCefOsrComponentClass = uiComponent.javaClass
                if (jbCefOsrComponentClass.simpleName == "JBCefOsrComponent") {
                    // Get the setBrowser method, including private methods
                    val setBrowserMethod: Method = jbCefOsrComponentClass.getMethod("setBrowser", CefBrowser::class.java)

                    // Make the method accessible (bypass access restrictions)
                    setBrowserMethod.isAccessible = true

                    // Invoke the method with the browser instance
                    setBrowserMethod.invoke(uiComponent, devTools)
                }
            } catch (e: NoSuchMethodException) {
                // Handle case where setBrowser method is not found
                throw RuntimeException("setBrowser method not found on JBCefOsrComponent", e)
            } catch (e: Exception) {
                // Handle other potential reflection errors
                throw RuntimeException("Failed to invoke setBrowser method", e)
            }
        }
    }

    fun reload() {
        browser.cefBrowser.reload()
    }

    fun openDevTools() {
        if (myDevtoolsFrame != null) {
            myDevtoolsFrame?.toFront()
            return
        }

        val comp = browser.component
        val ancestor = comp.let { SwingUtilities.getWindowAncestor(it) }
            ?: KeyboardFocusManager.getCurrentKeyboardFocusManager().focusedWindow
            ?: return

        val bounds = ancestor.graphicsConfiguration.bounds

        // 使用 build() 替代 createBrowser()
        myDevTools = JBCefBrowser.createBuilder()
            .setCefBrowser(browser.cefBrowser.devTools)
            .setClient(browser.jbCefClient)
            .build()
        patchDevTool(browser.cefBrowser.devTools)

        // 注册 DevTools browser 到主 browser 的 Disposable
        Disposer.register(browser, myDevTools!!)

        myDevtoolsFrame = JDialog(ancestor).apply {
            title = "JCEF DevTools"
            defaultCloseOperation = WindowConstants.DISPOSE_ON_CLOSE
            setBounds(
                bounds.width / 4 + 100,
                bounds.height / 4 + 100,
                bounds.width / 2,
                bounds.height / 2
            )
            layout = BorderLayout()

            add(myDevTools!!.component, BorderLayout.CENTER)

            addWindowListener(object : WindowAdapter() {
                override fun windowClosing(e: WindowEvent) {
                    cleanupDevTools()
                }
            })

            isVisible = true
        }
    }

    private fun cleanupDevTools() {
        myDevtoolsFrame?.let {
            it.isVisible = false
            it.dispose()
            myDevtoolsFrame = null
        }
        
        myDevTools = null  // 不需要手动 dispose，因为已经注册到主 browser 的 Disposable
    }

    fun dispose() {
        cleanupDevTools()

        // 清理其他资源
        try {
            // 确保在销毁浏览器之前停止所有可能的操作
            pageLoaded = false
            receivedInitPage = false
            onInitPage = null
            eventHandlers = emptyMap()
            WebViewManager.unregister(project, this)
            // 最后销毁浏览器
            Disposer.dispose(browser)
        } catch (e: Exception) {
            println("销毁 TocoBrowser 时出错: ${e.message}")
        }
    }

    companion object {
        private val pendingResponseMap = ConcurrentHashMap<String, CompletableDeferred<JsonObject>>()
    }

    /**
     * 获取浏览器中的所有 cookies
     *
     * @param callback 回调函数，接收 cookie 字符串
     */
    fun getCookies(callback: (String) -> Unit) {
        val cookieManager = CefCookieManager.getGlobalManager()
        cookieManager.visitAllCookies { cookie, count, total, delete ->
            val cookieString = "${cookie.name}=${cookie.value}"
            callback(cookieString)
            true // 继续访问下一个 cookie
        }
    }

    fun getURL(): String {
        // 在2025.1.3上出现了NPE，貌似是browser或者cefBrowser为null
        // 这里兜个底，不要因为有warning而去掉这个判断
        if (browser == null || browser.cefBrowser == null) {
            return url
        }
        return browser.cefBrowser.url
    }

    fun getModified(): Boolean {
        return modified
    }
}
